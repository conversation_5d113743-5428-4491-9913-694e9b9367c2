# frozen_string_literal: true

module AtharAuth
  module ResourceAuthorization
    extend ActiveSupport::Concern

    class_methods do
      # Method call style (like acts_as_approvable)
      def authorize_resources(options = {})
        # Store configuration
        class_attribute :authorization_config, default: options

        # Set up before_actions based on configuration
        setup_authorization_callbacks(options)
      end

      private

      # Detect if an action is semantically a collection action
      # Collection actions operate on multiple resources and don't require a specific resource ID
      def detect_collection_action_semantically(action)
        action_sym = action.to_sym

        # Standard REST collection actions
        return true if action_sym == :index

        # Actions that typically don't require ID (collection-like)
        collection_like_actions = %i[new create search filter export import sequence]
        return true if collection_like_actions.include?(action_sym)

        # Check naming conventions that indicate collection actions
        action_str = action_sym.to_s
        collection_suffixes = %w[_list _all _search _export _import _bulk _summary _stats _analytics]
        collection_prefixes = %w[list_ search_ filter_ export_ import_ bulk_]

        return true if collection_suffixes.any? { |suffix| action_str.end_with?(suffix) }
        return true if collection_prefixes.any? { |prefix| action_str.start_with?(prefix) }

        # Default to single resource action for safety
        false
      end

      def setup_authorization_callbacks(options)
        # Default actions that get auto-authorization
        auto_actions = options[:only] || [:index, :show, :create, :update, :destroy]
        # Intelligently determine collection actions if not explicitly specified
        if options[:collection_actions]
          collection_actions = options[:collection_actions]
        else
          # Use semantic detection for collection actions
          collection_actions = auto_actions.select { |action| detect_collection_action_semantically(action) }
        end
        skip_actions = options[:except] || []

        Rails.logger.debug "[AtharAuth] Controller: #{self.name}"
        Rails.logger.debug "[AtharAuth] auto_actions: #{auto_actions}"
        Rails.logger.debug "[AtharAuth] collection_actions: #{collection_actions}"
        Rails.logger.debug "[AtharAuth] skip_actions: #{skip_actions}"

        # Remove skipped actions
        auto_actions -= skip_actions
        collection_actions -= skip_actions

        Rails.logger.debug "[AtharAuth] Final auto_actions: #{auto_actions}"
        Rails.logger.debug "[AtharAuth] Final collection_actions: #{collection_actions}"
        Rails.logger.debug "[AtharAuth] Single resource actions: #{auto_actions - collection_actions}"

        # Set up callbacks
        before_action :authorize_and_load_collection!, only: collection_actions
        before_action :authorize_and_load_resource!, only: (auto_actions - collection_actions)
      end
    end

    private

    def authorize_and_load_collection!
      Rails.logger.debug "[AtharAuth] authorize_and_load_collection! called for #{controller_name}##{action_name}"
      resource_class = controller_name.classify.constantize
      options = {}
      collection = authorize_collection(action_name.to_sym, resource_class, options)

      # Set instance variable with resource name (like @cases, @employees)
      instance_variable_set("@#{controller_name}", collection)
    end

    def authorize_and_load_resource!
      Rails.logger.debug "[AtharAuth] authorize_and_load_resource! called for #{controller_name}##{action_name}"
      resource_class = controller_name.classify.constantize
      options = {}
      resource = authorize_single_resource(action_name.to_sym, resource_class, options)

      # Set instance variable with singular resource name (like @case, @employee)
      instance_variable_set("@#{controller_name.singularize}", resource)
    end
  end
end
